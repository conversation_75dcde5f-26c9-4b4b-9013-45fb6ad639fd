'use client';

import { Button, Text } from 'frosted-ui';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';

export default function Navigation() {
  const router = useRouter();
  const pathname = usePathname();

  const isLeaderboard = pathname === '/leaderboard';
  const isCompetitions = pathname === '/competitions';

  console.log('Current pathname:', pathname); // Debug logging

  return (
    <div className="frosted-ui" data-accent-color="blue">
      <div className="flex items-center justify-between px-4 py-3" style={{
        borderBottom: "1px solid rgba(59, 130, 246, 0.2)",
        boxShadow: "0 2px 20px rgba(59, 130, 246, 0.1)",
        backgroundColor: "rgba(59, 130, 246, 0.02)"
      }}>
        {/* Back arrow */}
        <Link href="/">
          <Button variant="ghost" size="2">
            <Text size="3">←</Text>
          </Button>
        </Link>

        {/* Center tabs */}
        <div className="flex items-center space-x-1">
          <Link href="/competitions">
            <Button
              variant={isCompetitions ? "solid" : "ghost"}
              size="2"
              className="rounded-full"
            >
              <Text size="2" weight="medium">Competitions</Text>
              {isCompetitions && <div className="ml-1 w-2 h-2 bg-white rounded-full"></div>}
            </Button>
          </Link>

          <Link href="/leaderboard">
            <Button
              variant={isLeaderboard ? "solid" : "ghost"}
              size="2"
              className="rounded-full"
            >
              <Text size="2" weight="medium">Leaderboard</Text>
            </Button>
          </Link>
        </div>

        {/* Right side placeholder */}
        <div className="w-8"></div>
      </div>
    </div>
  );
}
