'use client';

import { Text, Head<PERSON>, <PERSON><PERSON>, Card } from 'frosted-ui';
import { useRouter } from 'next/navigation';
import Navigation from '@/components/Navigation';

export default function Page() {
	const router = useRouter();

	return (
		<div className="frosted-ui" data-accent-color="blue" data-has-background="true" style={{ backgroundColor: "#000000", minHeight: "100vh" }}>
			<Navigation />

			<div className="max-w-4xl mx-auto px-4 py-12">
				<div className="text-center mb-12">
					<Heading size="8" weight="bold" className="mb-4 text-white">
						Competition Platform
					</Heading>
					<Text size="4" className="max-w-2xl mx-auto text-neutral-300">
						Join exciting competitions, track your performance, and compete with the best traders worldwide
					</Text>
				</div>

				<div className="grid md:grid-cols-2 gap-8 mb-12">
					<Card>
						<div className="p-8 text-center">
							<div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
								<Text size="6">🏆</Text>
							</div>
							<Heading size="5" weight="bold" className="mb-4">
								View Leaderboard
							</Heading>
							<Text size="3" className="mb-6">
								Check your ranking and see how you stack up against other competitors
							</Text>
							<Button
								size="3"
								onClick={() => router.push('/leaderboard')}
							>
								View Rankings
							</Button>
						</div>
					</Card>

					<Card>
						<div className="p-8 text-center">
							<div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
								<Text size="6">⚡</Text>
							</div>
							<Heading size="5" weight="bold" className="mb-4">
								Join Competitions
							</Heading>
							<Text size="3" className="mb-6">
								Participate in trading competitions and win amazing prizes
							</Text>
							<Button
								size="3"
								onClick={() => router.push('/competitions')}
							>
								Browse Competitions
							</Button>
						</div>
					</Card>
				</div>

				<div className="grid grid-cols-3 gap-6">
					<Card>
						<div className="p-6 text-center">
							<Text size="6" weight="bold" className="mb-2">$500K+</Text>
							<Text size="2">Total Prizes</Text>
						</div>
					</Card>
					<Card>
						<div className="p-6 text-center">
							<Text size="6" weight="bold" className="mb-2">5,000+</Text>
							<Text size="2">Active Traders</Text>
						</div>
					</Card>
					<Card>
						<div className="p-6 text-center">
							<Text size="6" weight="bold" className="mb-2">50+</Text>
							<Text size="2">Competitions</Text>
						</div>
					</Card>
				</div>
			</div>
		</div>
	);
}
