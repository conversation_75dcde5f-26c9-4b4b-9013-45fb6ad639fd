'use client';

import { Text, Heading, Card, Button, Avatar } from 'frosted-ui';
import Navigation from '@/components/Navigation';
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";
import { loadFull } from "tsparticles";

// Mock data for competitions
const upcomingCompetitions: any[] = [];

const pastCompetitions = [
  {
    id: 1,
    winCondition: "Generate 100K views",
    endedOn: "Feb 18th",
    winner: {
      name: "<PERSON><PERSON>",
      avatar: "IM"
    }
  },
  {
    id: 2,
    winCondition: "Invite 15 users",
    endedOn: "Feb 15th",
    winner: {
      name: "<PERSON>",
      avatar: "J<PERSON>"
    }
  }
];

// Competition Card Component
function CompetitionCard({ competition, isUpcoming }: { competition: any, isUpcoming: boolean }) {
  return (
    <div className="bg-neutral-700/50 border border-neutral-600/50 rounded-lg p-5 hover:border-neutral-500/50 transition-colors cursor-pointer">
      {/* Top Row */}
      <div className="flex items-center justify-between mb-4">
        <Text size="3" weight="bold" className="text-white">
          {competition.winCondition}
        </Text>
        <div className="flex space-x-1">
          <div className="w-1.5 h-1.5 bg-neutral-500 rounded-full"></div>
          <div className="w-1.5 h-1.5 bg-neutral-500 rounded-full"></div>
          <div className="w-1.5 h-1.5 bg-neutral-500 rounded-full"></div>
        </div>
      </div>

      {/* Bottom Row */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-6">
          {/* Ended on */}
          <div>
            <Text size="1" className="text-neutral-400 mb-1">
              {isUpcoming ? "Ends on" : "Ended on"}
            </Text>
            <Text size="2" weight="bold" className="text-red-400">
              {competition.endedOn}
            </Text>
          </div>

          {/* Winner */}
          <div>
            <Text size="1" className="text-neutral-400 mb-1">
              {isUpcoming ? "Participants" : "Winner"}
            </Text>
            <div className="flex items-center space-x-2">
              <Avatar size="1" fallback={competition.winner.avatar} />
              <Text size="2" weight="bold" className="text-white">
                {competition.winner.name}
              </Text>
            </div>
          </div>
        </div>

        {/* Navigation Arrow */}
        <div className="text-blue-400">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
    </div>
  );
}

// Special button component with particles inside
function ParticleButton({ children, ...props }: any) {
  const particlesInit = async (main: any) => {
    await loadSlim(main);
  };

  return (
    <Button
      {...props}
      style={{
        position: "relative",
        overflow: "hidden",
        zIndex: 1
      }}
    >
      <span style={{ position: "relative", zIndex: 3 }}>
        {children}
      </span>
      <Particles
        id={`button-particles-${Math.random()}`}
        init={particlesInit}
        options={{
          background: { color: { value: "transparent" } },
          particles: {
            number: { value: 8 },
            color: { value: "#ffffff" },
            shape: { type: "circle" },
            opacity: { value: 0.6 },
            size: { value: 0.8 },
            move: {
              enable: true,
              speed: 0.5,
              direction: "none",
              random: true,
              straight: false,
              outModes: { default: "bounce" }
            },
            links: { enable: false }
          },
          fullScreen: { enable: false },
          detectRetina: true
        }}
        style={{
          position: "absolute",
          top: "2px",
          left: "2px",
          right: "2px",
          bottom: "2px",
          zIndex: 2,
          pointerEvents: "none",
          borderRadius: "inherit"
        }}
      />
    </Button>
  );
}

export default function CompetitionsPage() {
  const particlesInit = async (main: any) => {
    await loadFull(main);
  };

  return (
    <div className="frosted-ui" data-accent-color="blue" data-has-background="true" style={{ position: "relative", minHeight: "100vh", backgroundColor: "#000000" }}>
      <Particles
        id="particles"
        init={particlesInit}
        options={{
          background: {
            color: { value: "transparent" },
          },
          particles: {
            number: { value: 60 },
            color: { value: "#00ffcc" },
            shape: { type: "circle" },
            opacity: { value: 0.6 },
            size: { value: 3 },
            move: { 
              enable: true, 
              speed: 1.5,
              direction: "none",
              random: true,
              straight: false,
              outModes: { default: "bounce" }
            },
            links: {
              enable: true,
              distance: 120,
              color: "#00ffcc",
              opacity: 0.3,
              width: 1
            }
          },
          interactivity: {
            events: {
              onHover: {
                enable: true,
                mode: "repulse"
              }
            }
          }
        }}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 0
        }}
      />
      
      <div style={{ position: "relative", zIndex: 10 }}>
        <Navigation />

        <div className="px-4 py-8">
          <div className="max-w-4xl mx-auto">
            
            {/* Main Container with Shadows */}
            <div className="bg-neutral-800/90 backdrop-blur-sm rounded-2xl p-8 shadow-[0_-8px_32px_rgba(0,0,0,0.3),0_8px_32px_rgba(0,0,0,0.3)] border border-neutral-700/50">
              
              {/* Page Header */}
              <div className="text-center mb-12">
                <h1 className="text-3xl font-bold text-white mb-3">Competitions</h1>
                <p className="text-neutral-400 text-lg">Track your performance and see past results</p>
              </div>

              {/* Two Column Layout */}
              <div className="grid md:grid-cols-2 gap-8">
                
                {/* Upcoming Section */}
                <div className="space-y-6">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-2 h-8 bg-green-500 rounded-full"></div>
                    <h2 className="text-2xl font-bold text-white">Upcoming</h2>
                  </div>
                  
                  {upcomingCompetitions.length === 0 ? (
                    <div className="bg-neutral-700/50 border border-neutral-600/50 rounded-lg p-8 text-center">
                      <div className="w-16 h-16 mx-auto mb-4 bg-neutral-600/50 rounded-full flex items-center justify-center">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <p className="text-neutral-400 text-lg font-medium">
                        No upcoming competitions
                      </p>
                      <p className="text-neutral-500 text-sm mt-2">
                        Check back soon for new challenges
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {upcomingCompetitions.map((competition) => (
                        <CompetitionCard key={competition.id} competition={competition} isUpcoming={true} />
                      ))}
                    </div>
                  )}
                </div>

                {/* Past Section */}
                <div className="space-y-6">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-2 h-8 bg-blue-500 rounded-full"></div>
                    <h2 className="text-2xl font-bold text-white">Past</h2>
                  </div>
                  
                  <div className="space-y-4">
                    {pastCompetitions.map((competition) => (
                      <CompetitionCard key={competition.id} competition={competition} isUpcoming={false} />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
