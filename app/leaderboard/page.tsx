'use client';

import { Avatar, Text, Heading, Button } from 'frosted-ui';
import Navigation from '@/components/Navigation';
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";

// Mock data for competition leaderboard
const leaderboardData = [
  {
    rank: 1,
    name: "<PERSON>",
    username: "@shark",
    amount: 26000.00,
    isVerified: true
  },
  {
    rank: 2,
    name: "<PERSON>",
    username: "@methodicalstew",
    amount: 21345.50,
    isVerified: false
  },
  {
    rank: 3,
    name: "<PERSON>ha<PERSON>",
    username: "@shaq4257",
    amount: 14598.30,
    isVerified: false
  },
  {
    rank: 4,
    name: "<PERSON><PERSON>",
    username: "@ilyam<PERSON>ov",
    amount: 13196.00,
    isVerified: true
  },
  {
    rank: 5,
    name: "Savnatra",
    username: "@savnatra",
    amount: 11432.26,
    isVerified: false
  },
  {
    rank: 6,
    name: "<PERSON>",
    username: "@user673297",
    amount: 9200.00,
    isVerified: false
  },
  {
    rank: 7,
    name: "<PERSON><PERSON>",
    username: "@amiright",
    amount: 5190.00,
    isVerified: false
  },
  {
    rank: 8,
    name: "<PERSON>",
    username: "@abconsults",
    amount: 4105.30,
    isVerified: false
  }
];



// Compact Competition Card Component
function CompetitionCard({ title = "Money Earned" }: { title?: string }) {
  return (
    <div className="w-96 max-w-md relative">
      {/* Background Grey Box - Properly aligned and touching with clean border */}
      <div
        className="absolute -top-1 left-0 right-0 h-24 bg-gradient-to-br from-[rgba(255,255,255,0.12)] to-[rgba(255,255,255,0.06)] rounded-t-xl backdrop-blur-sm"
        style={{
          border: '1px solid rgba(255,255,255,0.15)',
          borderBottom: 'none',
          boxShadow: '0 -2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.1)'
        }}
      ></div>

      {/* Title and Buttons - Above Card */}
      <div className="relative z-10 pt-4 px-4 pb-2">
        <Heading size="4" weight="bold" className="mb-3 text-white" style={{ letterSpacing: '-0.02em' }}>
          {title}
        </Heading>

        {/* Compact Button Row */}
        <div className="flex items-center space-x-2">
          <Button
            variant="solid"
            size="1"
            className="rounded-full transition-all duration-200 hover:scale-105"
            style={{
              background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
              boxShadow: '0 2px 8px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.8)',
              border: '1px solid rgba(0,0,0,0.1)'
            }}
          >
            {/* Money Icon */}
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1.5">
              <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 1V6M12 18V23M4.22 4.22L7.76 7.76M16.24 16.24L19.78 19.78M1 12H6M18 12H23M4.22 19.78L7.76 16.24M16.24 7.76L19.78 4.22" stroke="currentColor" strokeWidth="2"/>
            </svg>
            <Text size="1" weight="bold" className="text-black">Money earned</Text>
          </Button>

          <Button
            variant="ghost"
            size="1"
            className="rounded-full transition-all duration-200 hover:scale-105"
            style={{
              background: 'linear-gradient(135deg, rgba(251, 146, 60, 0.2) 0%, rgba(251, 146, 60, 0.1) 100%)',
              border: '1px solid rgba(251, 146, 60, 0.3)',
              color: 'rgba(251, 146, 60, 1)',
              boxShadow: '0 2px 4px rgba(251, 146, 60, 0.2)'
            }}
          >
            {/* Views Icon */}
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1.5">
              <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
            </svg>
            <Text size="1" weight="bold">Views</Text>
          </Button>

          <Button
            variant="ghost"
            size="1"
            className="rounded-full transition-all duration-200 hover:scale-105"
            style={{
              background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(34, 197, 94, 0.1) 100%)',
              border: '1px solid rgba(34, 197, 94, 0.3)',
              color: 'rgba(34, 197, 94, 1)',
              boxShadow: '0 2px 4px rgba(34, 197, 94, 0.2)'
            }}
          >
            {/* My Icon */}
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1.5">
              <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
            </svg>
            <Text size="1" weight="bold">My</Text>
          </Button>
        </div>
      </div>

      {/* Main Card - Connected seamlessly */}
      <div
        className="bg-gradient-to-br from-[rgba(255,255,255,0.08)] to-[rgba(255,255,255,0.02)] rounded-xl p-5 backdrop-blur-md h-72 overflow-hidden relative"
        style={{
          border: '1px solid rgba(255,255,255,0.15)',
          boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)'
        }}
      >
        {/* Stats Section - Inside Card Header */}
        <div className="flex items-center justify-between mb-5 pb-4 border-b border-[rgba(255,255,255,0.15)]">
          <div className="text-left">
            <Text size="1" className="text-[rgba(255,255,255,0.7)] mb-1 font-medium uppercase tracking-wide">Participants</Text>
            <Heading size="3" weight="bold" className="text-white" style={{ fontFeatureSettings: '"tnum"' }}>{leaderboardData.length}</Heading>
          </div>
          <div className="text-center">
            <Text size="1" className="text-[rgba(255,255,255,0.7)] mb-1 font-medium uppercase tracking-wide">Prize Pool</Text>
            <Heading size="3" weight="bold" className="text-white" style={{ fontFeatureSettings: '"tnum"' }}>
              ${leaderboardData.reduce((sum, user) => sum + user.amount, 0).toLocaleString()}
            </Heading>
          </div>
          <div className="text-right">
            <Text size="1" className="text-[rgba(255,255,255,0.7)] mb-1 font-medium uppercase tracking-wide">Average</Text>
            <Heading size="3" weight="bold" className="text-white" style={{ fontFeatureSettings: '"tnum"' }}>
              ${Math.round(leaderboardData.reduce((sum, user) => sum + user.amount, 0) / leaderboardData.length).toLocaleString()}
            </Heading>
          </div>
        </div>

        {/* Enhanced Leaderboard List */}
        <div className="space-y-3">
          {leaderboardData.slice(0, 5).map((user, index) => (
            <CompactLeaderboardRow key={`${user.rank}-${user.username}`} user={user} index={index} />
          ))}
        </div>

        {/* Show more indicator */}
        {leaderboardData.length > 5 && (
          <div className="mt-4 pt-3 border-t border-[rgba(255,255,255,0.1)] text-center">
            <Text size="1" className="text-[rgba(255,255,255,0.6)] font-medium">
              +{leaderboardData.length - 5} more participants
            </Text>
          </div>
        )}
      </div>
    </div>
  );
}

export default function LeaderboardPage() {
  const particlesInit = async (main: any) => {
    await loadSlim(main);
  };

  return (
    <div className="frosted-ui" data-accent-color="blue" data-has-background="true" style={{
      position: "relative",
      height: "100vh",
      overflow: "hidden",
      backgroundColor: "#000000"
    }}>
      <Particles
        id="particles"
        init={particlesInit}
        options={{
          background: {
            color: { value: "transparent" },
          },
          particles: {
            number: { value: 15 },
            color: { value: "#ffffff" },
            shape: { type: "circle" },
            opacity: { value: 0.08 },
            size: { value: 1 },
            move: {
              enable: true,
              speed: 0.3,
              direction: "none",
              random: true,
              straight: false,
              outModes: { default: "bounce" }
            },
            links: {
              enable: false
            }
          }
        }}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 0
        }}
      />

      <div style={{ position: "relative", zIndex: 10, height: "100%" }}>
        <Navigation />

        {/* Proper spacing from navigation */}
        <div className="px-6 py-8 pt-20">
          <div className="max-w-7xl mx-auto">

            {/* Competition Cards Grid - Starting from top left */}
            <div className="flex flex-wrap gap-6">
              <CompetitionCard title="Money Earned" />
              <CompetitionCard title="Views Generated" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Enhanced Compact Leaderboard Row Component
function CompactLeaderboardRow({ user, index }: { user: any, index: number }) {
  const rankColors = [
    { bg: 'from-yellow-400 to-yellow-600', shadow: 'rgba(255, 215, 0, 0.4)', border: 'rgba(255, 215, 0, 0.6)' }, // 1st place
    { bg: 'from-gray-300 to-gray-500', shadow: 'rgba(192, 192, 192, 0.4)', border: 'rgba(192, 192, 192, 0.6)' },     // 2nd place
    { bg: 'from-orange-400 to-orange-600', shadow: 'rgba(255, 165, 0, 0.4)', border: 'rgba(255, 165, 0, 0.6)' }  // 3rd place
  ];

  return (
    <div
      className="bg-gradient-to-r from-[rgba(255,255,255,0.08)] to-[rgba(255,255,255,0.04)] border border-[rgba(255,255,255,0.12)] rounded-lg p-3 hover:border-[rgba(255,255,255,0.25)] transition-all duration-300 cursor-pointer hover:bg-gradient-to-r hover:from-[rgba(255,255,255,0.12)] hover:to-[rgba(255,255,255,0.06)] group hover:scale-[1.02]"
      style={{
        boxShadow: '0 2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.1)'
      }}
    >
      <div className="flex items-center justify-between">
        {/* Left side - Rank & User Info */}
        <div className="flex items-center space-x-3">
          {/* Enhanced Rank Badge */}
          <div className="relative">
            {index < 3 ? (
              <div
                className={`w-8 h-8 rounded-full bg-gradient-to-br ${rankColors[index].bg} flex items-center justify-center shadow-lg border-2`}
                style={{
                  boxShadow: `0 4px 12px ${rankColors[index].shadow}, inset 0 1px 0 rgba(255,255,255,0.3)`,
                  borderColor: rankColors[index].border
                }}
              >
                <Text size="1" weight="bold" className="text-white" style={{ fontSize: '0.75rem', fontFeatureSettings: '"tnum"' }}>
                  {user.rank}
                </Text>
              </div>
            ) : (
              <div
                className="w-8 h-8 rounded-full bg-gradient-to-br from-[rgba(255,255,255,0.15)] to-[rgba(255,255,255,0.08)] flex items-center justify-center border border-[rgba(255,255,255,0.2)]"
                style={{
                  boxShadow: '0 2px 6px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.2)'
                }}
              >
                <Text size="1" weight="bold" className="text-[rgba(255,255,255,0.9)]" style={{ fontSize: '0.75rem', fontFeatureSettings: '"tnum"' }}>
                  {user.rank}
                </Text>
              </div>
            )}
            {index === 0 && (
              <div
                className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full flex items-center justify-center border border-yellow-200"
                style={{ boxShadow: '0 2px 4px rgba(255, 215, 0, 0.4)' }}
              >
                <span className="text-yellow-900 text-xs">👑</span>
              </div>
            )}
          </div>

          {/* Enhanced Profile Picture */}
          <Avatar
            size="2"
            fallback={user.name.charAt(0)}
            className="ring-2 ring-[rgba(255,255,255,0.2)] ring-offset-2 ring-offset-transparent"
          />

          {/* Enhanced User Info */}
          <div className="min-w-0 flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <Text size="2" weight="bold" className="text-white group-hover:text-[rgba(255,255,255,0.95)] transition-colors truncate" style={{ letterSpacing: '-0.01em' }}>
                {user.name}
              </Text>
              {user.isVerified && (
                <div
                  className="w-4 h-4 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 border border-blue-300"
                  style={{ boxShadow: '0 2px 4px rgba(59, 130, 246, 0.4)' }}
                >
                  <svg width="8" height="8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              )}
            </div>
            <Text size="1" className="text-[rgba(255,255,255,0.6)] group-hover:text-[rgba(255,255,255,0.8)] transition-colors font-medium">
              {user.username}
            </Text>
          </div>
        </div>

        {/* Right side - Enhanced Amount Display */}
        <div
          className="flex items-center gap-2 bg-gradient-to-r from-[rgba(34,197,94,0.15)] to-[rgba(34,197,94,0.08)] border border-[rgba(34,197,94,0.3)] rounded-lg px-3 py-2 min-w-0"
          style={{
            boxShadow: 'inset 0 1px 0 rgba(34,197,94,0.2), 0 2px 8px rgba(34,197,94,0.15)',
          }}
        >
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-400">
            <line x1="12" y1="1" x2="12" y2="23" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <path d="M17 5H9.5C8.11929 5 7 6.11929 7 7.5S8.11929 10 9.5 10H14.5C15.8807 10 17 11.1193 17 12.5S15.8807 15 14.5 15H7" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
          <span
            className="text-white font-bold tabular-nums"
            style={{
              fontSize: '0.875rem',
              fontFeatureSettings: '"tnum"',
              textShadow: '0 1px 2px rgba(0,0,0,0.3)'
            }}
          >
            ${user.amount.toLocaleString()}
          </span>
        </div>
      </div>
    </div>
  );
}