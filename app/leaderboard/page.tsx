'use client';

import { Avatar, Text, Heading, Button } from 'frosted-ui';
import Navigation from '@/components/Navigation';
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";

// Mock data for competition leaderboard
const leaderboardData = [
  {
    rank: 1,
    name: "<PERSON>",
    username: "@shark",
    amount: 26000.00,
    isVerified: true
  },
  {
    rank: 2,
    name: "<PERSON>",
    username: "@methodicalstew",
    amount: 21345.50,
    isVerified: false
  },
  {
    rank: 3,
    name: "<PERSON>ha<PERSON>",
    username: "@shaq4257",
    amount: 14598.30,
    isVerified: false
  },
  {
    rank: 4,
    name: "<PERSON><PERSON>",
    username: "@ilyam<PERSON>ov",
    amount: 13196.00,
    isVerified: true
  },
  {
    rank: 5,
    name: "Savnat<PERSON>",
    username: "@savnatra",
    amount: 11432.26,
    isVerified: false
  },
  {
    rank: 6,
    name: "<PERSON>",
    username: "@user673297",
    amount: 9200.00,
    isVerified: false
  },
  {
    rank: 7,
    name: "<PERSON><PERSON>",
    username: "@amiright",
    amount: 5190.00,
    isVerified: false
  },
  {
    rank: 8,
    name: "<PERSON>",
    username: "@abconsults",
    amount: 4105.30,
    isVerified: false
  }
];



// Compact Competition Card Component
function CompetitionCard({ title = "Money Earned" }: { title?: string }) {
  return (
    <div className="w-80 max-w-sm relative">
      {/* Background Grey Box - Behind title and buttons */}
      <div className="absolute -top-2 -left-2 w-full h-20 bg-[rgba(255,255,255,0.08)] border border-[rgba(255,255,255,0.15)] rounded-lg backdrop-blur-sm"></div>

      {/* Title and Buttons - Above Card */}
      <div className="relative mb-1 p-3">
        <Heading size="3" weight="bold" className="mb-2 text-white">
          {title}
        </Heading>

        {/* Compact Button Row */}
        <div className="flex items-center space-x-2">
          <Button
            variant="solid"
            size="1"
            className="rounded-full bg-white hover:bg-gray-100 transition-colors"
            style={{
              background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
              boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
              border: '1px solid #d1d5db'
            }}
          >
            <Text size="1" weight="bold" className="text-black">Money earned</Text>
          </Button>

          <Button
            variant="ghost"
            size="1"
            className="rounded-full transition-all duration-200"
            style={{
              background: 'linear-gradient(135deg, rgba(251, 146, 60, 0.15) 0%, rgba(251, 146, 60, 0.08) 100%)',
              border: '1px solid rgb(12, 12, 12)',
              color: 'rgba(251, 146, 60, 0.9)'
            }}
          >
            <Text size="1" weight="bold">Views</Text>
          </Button>

          <Button
            variant="ghost"
            size="1"
            className="rounded-full transition-all duration-200"
            style={{
              background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 16, 16, 0.8) 100%)',
              border: '1px solid rgb(12, 12, 12)',
              color: 'rgba(34, 197, 94, 0.9)'
            }}
          >
            <Text size="1" weight="bold">My</Text>
          </Button>
        </div>
      </div>

      {/* Main Card */}
      <div className="bg-[rgba(255,255,255,0.02)] border border-[rgba(255,255,255,0.1)] rounded-lg p-4 backdrop-blur-sm h-64 overflow-hidden">
        {/* Stats Section - Inside Card Header */}
        <div className="flex items-center justify-between mb-4 pb-3 border-b border-[rgba(255,255,255,0.1)]">
          <div className="text-left">
            <Text size="1" className="text-[rgba(255,255,255,0.6)] mb-1">Participants</Text>
            <Heading size="2" weight="bold" className="text-white">{leaderboardData.length}</Heading>
          </div>
          <div className="text-center">
            <Text size="1" className="text-[rgba(255,255,255,0.6)] mb-1">Prize Pool</Text>
            <Heading size="2" weight="bold" className="text-white">
              ${(leaderboardData.reduce((sum, user) => sum + user.amount, 0) / 1000).toFixed(0)}k
            </Heading>
          </div>
          <div className="text-right">
            <Text size="1" className="text-[rgba(255,255,255,0.6)] mb-1">Average</Text>
            <Heading size="2" weight="bold" className="text-white">
              ${(leaderboardData.reduce((sum, user) => sum + user.amount, 0) / leaderboardData.length / 1000).toFixed(0)}k
            </Heading>
          </div>
        </div>

        {/* Compact Leaderboard List - Show only top 5 */}
        <div className="space-y-2">
          {leaderboardData.slice(0, 5).map((user, index) => (
            <CompactLeaderboardRow key={`${user.rank}-${user.username}`} user={user} index={index} />
          ))}
        </div>

        {/* Show more indicator */}
        {leaderboardData.length > 5 && (
          <div className="mt-3 pt-2 border-t border-[rgba(255,255,255,0.1)] text-center">
            <Text size="1" className="text-[rgba(255,255,255,0.6)]">
              +{leaderboardData.length - 5} more participants
            </Text>
          </div>
        )}
      </div>
    </div>
  );
}

export default function LeaderboardPage() {
  const particlesInit = async (main: any) => {
    await loadSlim(main);
  };

  return (
    <div className="frosted-ui" data-accent-color="blue" data-has-background="true" style={{
      position: "relative",
      height: "100vh",
      overflow: "hidden",
      backgroundColor: "#000000"
    }}>
      <Particles
        id="particles"
        init={particlesInit}
        options={{
          background: {
            color: { value: "transparent" },
          },
          particles: {
            number: { value: 15 },
            color: { value: "#ffffff" },
            shape: { type: "circle" },
            opacity: { value: 0.08 },
            size: { value: 1 },
            move: {
              enable: true,
              speed: 0.3,
              direction: "none",
              random: true,
              straight: false,
              outModes: { default: "bounce" }
            },
            links: {
              enable: false
            }
          }
        }}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 0
        }}
      />

      <div style={{ position: "relative", zIndex: 10, height: "100%" }}>
        <Navigation />

        {/* Proper spacing from navigation */}
        <div className="px-6 py-8 pt-20">
          <div className="max-w-7xl mx-auto">

            {/* Competition Cards Grid - Starting from top left */}
            <div className="flex flex-wrap gap-6">
              <CompetitionCard title="Money Earned" />
              <CompetitionCard title="Views Generated" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Compact Leaderboard Row Component for smaller cards
function CompactLeaderboardRow({ user, index }: { user: any, index: number }) {
  const rankColors = [
    'from-yellow-400 to-yellow-600', // 1st place
    'from-gray-300 to-gray-500',     // 2nd place
    'from-orange-400 to-orange-600'  // 3rd place
  ];

  return (
    <div className="bg-[rgba(255,255,255,0.03)] border border-[rgba(255,255,255,0.08)] rounded-md p-2 hover:border-[rgba(255,255,255,0.15)] transition-all duration-200 cursor-pointer hover:bg-[rgba(255,255,255,0.05)] group">
      <div className="flex items-center justify-between">
        {/* Left side - Rank & User Info */}
        <div className="flex items-center space-x-2">
          {/* Compact Rank Badge */}
          <div className="relative">
            {index < 3 ? (
              <div className={`w-5 h-5 rounded-full bg-gradient-to-br ${rankColors[index]} flex items-center justify-center shadow-sm`}>
                <Text size="1" weight="bold" className="text-white text-xs">
                  {user.rank}
                </Text>
              </div>
            ) : (
              <div className="w-5 h-5 rounded-full bg-[rgba(255,255,255,0.08)] flex items-center justify-center border border-[rgba(255,255,255,0.1)]">
                <Text size="1" weight="medium" className="text-[rgba(255,255,255,0.8)] text-xs">
                  {user.rank}
                </Text>
              </div>
            )}
            {index === 0 && (
              <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-yellow-400 rounded-full flex items-center justify-center">
                <Text size="1" className="text-yellow-900 text-xs">👑</Text>
              </div>
            )}
          </div>

          {/* Compact Profile Picture */}
          <Avatar size="1" fallback={user.name.charAt(0)} />

          {/* Compact User Info */}
          <div className="min-w-0 flex-1">
            <div className="flex items-center space-x-1">
              <Text size="1" weight="bold" className="text-white group-hover:text-[rgba(255,255,255,0.9)] transition-colors truncate">
                {user.name}
              </Text>
              {user.isVerified && (
                <div className="w-2 h-2 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <Text size="1" className="text-white text-xs">✓</Text>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right side - Compact Amount */}
        <div
          className="flex items-center gap-1 bg-[#0f0f0f] border border-[#2a2a2a] rounded px-1.5 py-0.5 min-w-0 w-fit"
          style={{
            fontSize: '0.75rem',
            fontWeight: 600,
            boxShadow: 'inset 0 0 4px rgba(34, 197, 94, 0.12)',
          }}
        >
          <span className="text-white tabular-nums">${(user.amount / 1000).toFixed(0)}k</span>
        </div>
      </div>
    </div>
  );
}