'use client';

import { Ava<PERSON>, Text, Heading, Button } from 'frosted-ui';
import Navigation from '@/components/Navigation';
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";

// Mock data for competition leaderboard
const leaderboardData = [
  {
    rank: 1,
    name: "<PERSON>",
    username: "@shark",
    amount: 26000.00,
    isVerified: true
  },
  {
    rank: 2,
    name: "<PERSON>",
    username: "@methodicalstew",
    amount: 21345.50,
    isVerified: false
  },
  {
    rank: 3,
    name: "<PERSON>ha<PERSON>",
    username: "@shaq4257",
    amount: 14598.30,
    isVerified: false
  },
  {
    rank: 4,
    name: "<PERSON><PERSON>",
    username: "@ilyam<PERSON>ov",
    amount: 13196.00,
    isVerified: true
  },
  {
    rank: 5,
    name: "<PERSON>v<PERSON><PERSON>",
    username: "@savnatra",
    amount: 11432.26,
    isVerified: false
  },
  {
    rank: 6,
    name: "<PERSON>",
    username: "@user673297",
    amount: 9200.00,
    isVerified: false
  },
  {
    rank: 7,
    name: "<PERSON><PERSON>",
    username: "@amiright",
    amount: 5190.00,
    isVerified: false
  },
  {
    rank: 8,
    name: "<PERSON>",
    username: "@abconsults",
    amount: 4105.30,
    isVerified: false
  }
];

// AmountBox component
function AmountBox({ amount }: { amount: number }) {
  return (
    <div
      className="flex items-center gap-1 bg-[#0f0f0f] border border-[#2a2a2a] rounded-md px-2 py-[2px] min-w-0 w-fit relative"
      style={{ 
        fontSize: '0.85rem', 
        fontWeight: 600,
        boxShadow: 'inset 0 0 6px rgba(34, 197, 94, 0.12)',
      }}
    >
      <span className="text-white tabular-nums">${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
    </div>
  );
}

export default function LeaderboardPage() {
  const particlesInit = async (main: any) => {
    await loadSlim(main);
  };

  return (
    <div className="frosted-ui" data-accent-color="blue" data-has-background="true" style={{
      position: "relative",
      height: "100vh",
      overflow: "hidden",
      backgroundColor: "#000000"
    }}>
      <Particles
        id="particles"
        init={particlesInit}
        options={{
          background: {
            color: { value: "transparent" },
          },
          particles: {
            number: { value: 15 },
            color: { value: "#ffffff" },
            shape: { type: "circle" },
            opacity: { value: 0.08 },
            size: { value: 1 },
            move: {
              enable: true,
              speed: 0.3,
              direction: "none",
              random: true,
              straight: false,
              outModes: { default: "bounce" }
            },
            links: {
              enable: false
            }
          }
        }}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 0
        }}
      />
      
      <div style={{ position: "relative", zIndex: 10, height: "100%" }}>
        <Navigation />

        <div className="px-4 py-4">
          <div className="max-w-2xl mx-auto">
            
            {/* Compact Leaderboard Card */}
            <div className="bg-[rgba(255,255,255,0.02)] border border-[rgba(255,255,255,0.1)] rounded-xl p-6 backdrop-blur-sm">
              
              {/* Page Header Section */}
              <div className="text-center mb-6">
                <Heading size="5" weight="bold" className="mb-2 text-white">
                  Money Earned
                </Heading>
                <Text size="2" className="text-[rgba(255,255,255,0.7)]">
                  Track your performance and see how you rank
                </Text>
              </div>

              {/* Enhanced Stats Navigation Section */}
              <div className="flex items-center justify-center space-x-3 mb-6">
                {/* Money Earned Button - White with Black Text */}
                <Button 
                  variant="solid" 
                  size="2" 
                  className="rounded-full bg-white hover:bg-gray-100 transition-colors"
                  style={{
                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    border: '1px solid #d1d5db'
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <Text size="1" weight="bold" className="text-black">Money earned</Text>
                </Button>

                {/* Views Generated Button - Low Opacity Orange */}
                <Button 
                  variant="ghost" 
                  size="2" 
                  className="rounded-full transition-all duration-200"
                  style={{
                    background: 'linear-gradient(135deg, rgba(251, 146, 60, 0.15) 0%, rgba(251, 146, 60, 0.08) 100%)',
                    border: '1px solid rgb(12, 12, 12)',
                    color: 'rgba(251, 146, 60, 0.9)'
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
                  </svg>
                  <Text size="1" weight="bold">Views generated</Text>
                </Button>

                {/* My Button - Cool Green Gradient */}
                <Button 
                  variant="ghost" 
                  size="2" 
                  className="rounded-full transition-all duration-200"
                  style={{
                    background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 16, 16, 0.8) 100%)',
                    border: '1px solid rgb(12, 12, 12)',
                    color: 'rgba(34, 197, 94, 0.9)'
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                    <path d="M3 3V21H21V3H3ZM7 7H9V17H7V7ZM11 11H13V15H11V11ZM15 9H17V15H15V9Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <Text size="1" weight="bold">My</Text>
                </Button>
              </div>

              {/* Compact Leaderboard List */}
              <div className="space-y-3">
                {leaderboardData.map((user, index) => (
                  <LeaderboardRow key={`${user.rank}-${user.username}`} user={user} index={index} />
                ))}
              </div>

              {/* Bottom Stats Section */}
              <div className="mt-6 pt-4 border-t border-[rgba(255,255,255,0.1)]">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <Text size="1" className="text-[rgba(255,255,255,0.6)] mb-1">Total Participants</Text>
                    <Heading size="3" weight="bold" className="text-white">{leaderboardData.length}</Heading>
                  </div>
                  <div>
                    <Text size="1" className="text-[rgba(255,255,255,0.6)] mb-1">Total Prize Pool</Text>
                    <Heading size="3" weight="bold" className="text-white">
                      ${leaderboardData.reduce((sum, user) => sum + user.amount, 0).toLocaleString()}
                    </Heading>
                  </div>
                  <div>
                    <Text size="1" className="text-[rgba(255,255,255,0.6)] mb-1">Average Earnings</Text>
                    <Heading size="3" weight="bold" className="text-white">
                      ${(leaderboardData.reduce((sum, user) => sum + user.amount, 0) / leaderboardData.length).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    </Heading>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Compact Leaderboard Row Component
function LeaderboardRow({ user, index }: { user: any, index: number }) {
  const rankColors = [
    'from-yellow-400 to-yellow-600', // 1st place
    'from-gray-300 to-gray-500',     // 2nd place
    'from-orange-400 to-orange-600'  // 3rd place
  ];

  return (
    <div className="bg-[rgba(255,255,255,0.03)] border border-[rgba(255,255,255,0.08)] rounded-lg p-3 hover:border-[rgba(255,255,255,0.15)] transition-all duration-200 cursor-pointer hover:bg-[rgba(255,255,255,0.05)] group">
      <div className="flex items-center justify-between">
        {/* Left side - Rank & User Info */}
        <div className="flex items-center space-x-3">
          {/* Enhanced Rank Badge */}
          <div className="relative">
            {index < 3 ? (
              <div className={`w-7 h-7 rounded-full bg-gradient-to-br ${rankColors[index]} flex items-center justify-center shadow-md`}>
                <Text size="1" weight="bold" className="text-white">
                  {user.rank}
                </Text>
              </div>
            ) : (
              <div className="w-7 h-7 rounded-full bg-[rgba(255,255,255,0.08)] flex items-center justify-center border border-[rgba(255,255,255,0.1)]">
                <Text size="1" weight="medium" className="text-[rgba(255,255,255,0.8)]">
                  {user.rank}
                </Text>
              </div>
            )}
            {index === 0 && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center">
                <Text size="1" className="text-yellow-900 text-xs">👑</Text>
              </div>
            )}
          </div>

          {/* Profile Picture */}
          <Avatar size="2" fallback={user.name.charAt(0)} />

          {/* User Info */}
          <div>
            <div className="flex items-center space-x-2">
              <Text size="2" weight="bold" className="text-white group-hover:text-[rgba(255,255,255,0.9)] transition-colors">
                {user.name}
              </Text>
              {user.isVerified && (
                <div className="w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center">
                  <Text size="1" className="text-white">✓</Text>
                </div>
              )}
            </div>
            <Text size="1" className="text-[rgba(255,255,255,0.6)] group-hover:text-[rgba(255,255,255,0.8)] transition-colors">
              {user.username}
            </Text>
          </div>
        </div>

        {/* Right side - Amount */}
        <AmountBox amount={user.amount} />
      </div>
    </div>
  );
}